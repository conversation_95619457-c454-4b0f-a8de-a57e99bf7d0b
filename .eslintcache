[{"C:\\Users\\<USER>\\la-maison-jungle\\src\\index.js": "1", "C:\\Users\\<USER>\\la-maison-jungle\\src\\App.js": "2", "C:\\Users\\<USER>\\la-maison-jungle\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\la-maison-jungle\\src\\Banner.js": "4"}, {"size": 302, "mtime": 1752506239563, "results": "5", "hashOfConfig": "6"}, {"size": 501, "mtime": 1752680242722, "results": "7", "hashOfConfig": "6"}, {"size": 362, "mtime": 1752501860494, "results": "8", "hashOfConfig": "6"}, {"size": 351, "mtime": 1752680347874, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "12"}, "m9x1fe", {"filePath": "13", "messages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "12"}, {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "12"}, {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\la-maison-jungle\\src\\index.js", [], ["19", "20"], "C:\\Users\\<USER>\\la-maison-jungle\\src\\App.js", [], "C:\\Users\\<USER>\\la-maison-jungle\\src\\reportWebVitals.js", [], "C:\\Users\\<USER>\\la-maison-jungle\\src\\Banner.js", [], {"ruleId": "21", "replacedBy": "22"}, {"ruleId": "23", "replacedBy": "24"}, "no-native-reassign", ["25"], "no-negated-in-lhs", ["26"], "no-global-assign", "no-unsafe-negation"]