{"name": "la-maison-jungle", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "react": "^17.0.1", "react-dom": "^17.0.1", "react-scripts": "4.0.1", "web-vitals": "^0.2.4"}, "scripts": {"start": "SET NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "SET NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1"}, "resolutions": {"webpack": "4.44.2"}}